'use client';

import { useState, useEffect } from 'react';
import { X, Save, Loader2 } from 'lucide-react';
import { Product } from '@/types';
import { productService, categoryService } from '@/lib/database';
import { validateProduct, sanitizeProductData } from '@/lib/validation';
import ConfirmDialog, { useConfirmDialog } from './ConfirmDialog';

interface ProductEditFormProps {
  product?: Product | null;
  onClose: () => void;
  onSave: (product: Product) => void;
}

interface FormData {
  name: string;
  category: string;
  amazonUrl: string;
  images: string[];
  features: string;
  rating: number;
}

interface FormErrors {
  name?: string;
  category?: string;
  amazonUrl?: string;
  images?: string;
  features?: string;
  rating?: string;
}

export default function ProductEditForm({ product, onClose, onSave }: ProductEditFormProps) {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    category: '',
    amazonUrl: '',
    images: [],
    features: '',
    rating: 0
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  const confirmDialog = useConfirmDialog();

  const isEditing = !!product;

  useEffect(() => {
    loadCategories();
    if (product) {
      setFormData({
        name: product.name || '',
        category: product.category || '',
        amazonUrl: product.amazonUrl || '',
        images: product.images || [],
        features: product.features || '',
        rating: product.rating || 0
      });
    }
  }, [product]);

  const loadCategories = async () => {
    try {
      const data = await categoryService.getAll();
      setCategories(data);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const validateForm = (): boolean => {
    const validation = validateProduct(formData);
    setErrors(validation.errors);
    return validation.isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const productData = sanitizeProductData(formData);

      let savedProduct: Product;
      if (isEditing && product) {
        savedProduct = await productService.update(product.id, productData);
      } else {
        savedProduct = await productService.create(productData);
      }

      onSave(savedProduct);
      onClose();
    } catch (error) {
      console.error('Error saving product:', error);

      // 更友好的错误处理
      let errorMessage = '保存产品时出错，请重试';
      if (error instanceof Error) {
        if (error.message.includes('duplicate')) {
          errorMessage = '产品名称已存在，请使用不同的名称';
        } else if (error.message.includes('network')) {
          errorMessage = '网络连接错误，请检查网络后重试';
        } else if (error.message.includes('permission')) {
          errorMessage = '权限不足，请联系管理员';
        }
      }

      setErrors({ name: errorMessage });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleClose = () => {
    if (hasChanges) {
      confirmDialog.showConfirm({
        title: '确认关闭',
        message: '您有未保存的更改，确定要关闭吗？',
        confirmText: '关闭',
        cancelText: '继续编辑',
        type: 'warning',
        onConfirm: onClose
      });
    } else {
      onClose();
    }
  };

  // 删除了旧的特性管理函数，现在使用textarea

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-pink-600 text-white p-4 flex justify-between items-center">
          <h2 className="text-xl font-bold">
            {isEditing ? '编辑产品' : '新建产品'}
          </h2>
          <button
            onClick={handleClose}
            className="text-white hover:text-pink-200 text-2xl"
            disabled={loading}
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 产品名称 */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                产品名称 *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500 ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="请输入产品名称"
                disabled={loading}
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </div>

            {/* 产品分类 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                产品分类 *
              </label>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 ${
                  errors.category ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={loading}
              >
                <option value="">请选择分类</option>
                {categories.map((cat) => (
                  <option key={cat.slug} value={cat.slug}>
                    {cat.name}
                  </option>
                ))}
              </select>
              {errors.category && <p className="mt-1 text-sm text-red-600">{errors.category}</p>}
            </div>



            {/* Amazon链接 */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amazon链接 *
              </label>
              <input
                type="url"
                value={formData.amazonUrl}
                onChange={(e) => handleInputChange('amazonUrl', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500 ${
                  errors.amazonUrl ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="https://amazon.com.mx/dp/..."
                disabled={loading}
              />
              {errors.amazonUrl && <p className="mt-1 text-sm text-red-600">{errors.amazonUrl}</p>}
            </div>

            {/* 产品图片 */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                产品图片 URL
              </label>
              <div className="space-y-3">
                {formData.images.map((image, index) => (
                  <div key={index} className="flex gap-2">
                    <input
                      type="url"
                      value={image}
                      onChange={(e) => {
                        const newImages = [...formData.images];
                        newImages[index] = e.target.value;
                        setFormData(prev => ({ ...prev, images: newImages }));
                        setHasChanges(true);
                      }}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500"
                      placeholder="https://example.com/image.jpg"
                      disabled={loading}
                    />
                    <button
                      type="button"
                      onClick={() => {
                        const newImages = formData.images.filter((_, i) => i !== index);
                        setFormData(prev => ({ ...prev, images: newImages }));
                        setHasChanges(true);
                      }}
                      className="px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg"
                      disabled={loading}
                    >
                      删除
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => {
                    setFormData(prev => ({ ...prev, images: [...prev.images, ''] }));
                    setHasChanges(true);
                  }}
                  className="w-full px-3 py-2 border-2 border-dashed border-gray-300 text-gray-600 rounded-lg hover:border-pink-500 hover:text-pink-600"
                  disabled={loading}
                >
                  + 添加图片
                </button>
              </div>
              <p className="mt-1 text-sm text-gray-500">
                添加产品图片URL。第一张图片将作为主图显示。
              </p>
              {errors.images && <p className="mt-1 text-sm text-red-600">{errors.images}</p>}
            </div>

            {/* 产品特性 */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                产品特性描述
              </label>
              <textarea
                value={formData.features}
                onChange={(e) => handleInputChange('features', e.target.value)}
                rows={4}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500 ${
                  errors.features ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="描述产品的主要特性和功能..."
                disabled={loading}
              />
              <p className="mt-1 text-sm text-gray-500">
                详细描述产品的特性、功能和优势。
              </p>
              {errors.features && <p className="mt-1 text-sm text-red-600">{errors.features}</p>}
            </div>

            {/* 产品评分 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                产品评分
              </label>
              <input
                type="number"
                min="0"
                max="5"
                step="0.1"
                value={formData.rating}
                onChange={(e) => handleInputChange('rating', parseFloat(e.target.value) || 0)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 ${
                  errors.rating ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={loading}
              />
              <p className="mt-1 text-sm text-gray-500">
                评分范围：0.0 - 5.0
              </p>
              {errors.rating && <p className="mt-1 text-sm text-red-600">{errors.rating}</p>}
            </div>

          </div>

          {/* 提交按钮 */}
          <div className="mt-8 flex justify-end gap-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50"
              disabled={loading}
            >
              取消
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 disabled:opacity-50 flex items-center gap-2"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  {isEditing ? '更新产品' : '创建产品'}
                </>
              )}
            </button>
          </div>
        </form>

        {/* 确认对话框 */}
        <ConfirmDialog {...confirmDialog.dialog} />
      </div>
    </div>
  );
}
